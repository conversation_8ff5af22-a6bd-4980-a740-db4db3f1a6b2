#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统计信息API的脚本
"""

import requests
import json
import sys

def test_file_result_api():
    """测试文件结果API"""
    base_url = "http://localhost:5106"
    
    # 首先登录获取session
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    session = requests.Session()
    
    # 登录
    login_response = session.post(f"{base_url}/auth/login", json=login_data, allow_redirects=False)
    print(f"登录响应状态码: {login_response.status_code}")
    print(f"登录响应头: {login_response.headers}")

    # 检查是否重定向到主页（登录成功）
    if login_response.status_code not in [200, 302]:
        print(f"登录失败，状态码: {login_response.status_code}")
        print(f"响应内容: {login_response.text[:500]}")
        return False
    
    # 获取文件列表
    files_response = session.get(f"{base_url}/api/files")
    if files_response.status_code != 200:
        print("获取文件列表失败")
        return False
    
    files_data = files_response.json()
    print(f"文件列表响应: {files_data}")

    if not files_data.get('success'):
        print(f"获取文件列表失败: {files_data.get('message')}")
        return False

    files_list = files_data.get('data', {}).get('files', [])
    if not files_list or len(files_list) == 0:
        print("文件列表为空，需要先上传一些测试文件")
        return False

    # 获取第一个已完成的文件
    completed_files = [f for f in files_list if f.get('status') == 'completed']
    if not completed_files:
        print("没有已完成的文件，使用第一个文件")
        first_file = files_list[0]
    else:
        first_file = completed_files[0]
    file_id = first_file['id']
    
    print(f"测试文件ID: {file_id}")
    print(f"文件名: {first_file['filename']}")
    print(f"分析类型: {first_file['analysis_type']}")
    
    # 获取文件结果
    result_response = session.get(f"{base_url}/api/files/{file_id}/result")
    if result_response.status_code != 200:
        print(f"获取文件结果失败: {result_response.status_code}")
        return False
    
    result_data = result_response.json()
    if not result_data.get('success'):
        print(f"API返回失败: {result_data.get('message')}")
        return False
    
    # 检查返回的数据结构
    data = result_data['data']
    print("\n=== API返回的数据结构 ===")
    print(f"文件名: {data.get('filename')}")
    print(f"分析类型: {data.get('analysis_type')}")
    print(f"分析类型名称: {data.get('analysis_type_name')}")
    print(f"准确率分数: {data.get('accuracy_score')}")
    
    # 检查统计信息
    if 'stats' in data:
        stats = data['stats']
        print("\n=== 统计信息 ===")
        print(f"总字段数: {stats.get('total_fields')}")
        print(f"正确字段数: {stats.get('correct_fields')}")
        print(f"字段准确率: {stats.get('field_accuracy_rate')}")
        print(f"文件准确率: {stats.get('file_accuracy_rate')}")
    else:
        print("\n=== 未找到统计信息，检查原始数据 ===")
        print(f"field_accuracy: {data.get('field_accuracy')}")
        print(f"comparison_result: {data.get('comparison_result')}")
    
    # 保存完整的响应到文件
    with open('api_response.json', 'w', encoding='utf-8') as f:
        json.dump(result_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n完整响应已保存到 api_response.json")
    return True

if __name__ == "__main__":
    success = test_file_result_api()
    sys.exit(0 if success else 1)
